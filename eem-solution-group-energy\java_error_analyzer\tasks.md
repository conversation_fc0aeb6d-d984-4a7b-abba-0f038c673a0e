# Java 代码迁移问题处理任务计划

## 工作流概述

本任务计划用于处理 Java 代码迁移后的编译错误和兼容性问题，按照从底层到上层的顺序进行修复。生成一个详细的可以直接执行的修复任务列表。

## 阶段 1: 分析问题并生成修复任务

- [-] 1. 深度分析每个问题并确定解决方案

  - [x] 1.1 Import 问题/依赖配置问题分析和解决方案确定

    - **数据来源**: 直接读取 out\问题列表.md，判断是否属于 import 问题或者依赖配置问题
    - **完整性要求**: 必须处理所有 Import 类型问题，不允许遗漏任何一个问题
    - **分段处理策略**:
      - **严禁一次性加载**: 绝对不要一次性加载整个 out\questionlist.md 文件
      - **分段读取**: 按文件或问题类型分段读取，每次处理 10-20 个问题
      - **逐个验证**: 每处理一个问题立即验证是否正确分类和处理
      - **实时统计**: 处理过程中实时统计已处理问题数量，确保不遗漏
    - **详细信息**: 每个问题必须包含具体的文件名、行号、类名和解决方案
    - **工具使用**: 由大模型来思考和使用这些工具，以及何时使用
    - **知识库查找**: 导入的类需要先在知识库里查找，如果知识库有解决方案则直接使用，如果没有可以看建议，最后大模型结合工具处理
    - **类名查找**: 对每个 import 问题和依赖配置问题使用 class_name_finder.py 查找替代类
    - **解决方案分类**:
      - 对于单一匹配结果，直接确定替换方案（绿色标记 ✅）
      - 对于多个候选类，使用 class_file_reader.py 读取详细信息，基于类结构进行 AI 智能判断（黄色标记 🟡）
      - 无法确定最佳匹配的归类到"未识别"模块（红色标记 🔴）
    - **输出格式**: 生成详细的 out\task-import.md 文件，**统一按文件维度组织**：

      - **主结构**: 按文件名分组（与 out\问题列表.md 和 task-step.md 保持一致）
      - **文件内容**: 每个文件下列出该文件的所有 Import 问题
      - **问题详情**: 包含问题位置、解决方案分类（🟢🟡🔴）、具体修复操作
      - **格式示例**:

        ```markdown
        ## ClassesConfig.java

        ### Import 问题 1: ModelLabel 类导入 (🟢 绿色标记)

        - **问题位置**: 行号 3, 19
        - **解决方案**: import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;
        - **修复操作**: 在文件顶部添加导入语句
        ```

    - **强制完整性验证机制**:
      - **处理前验证**: 在开始处理前，先统计 out\问题列表.md 中 Import 问题的总数
      - **处理中验证**: 每处理完一个文件的问题，立即核对数量
      - **处理后验证**: 在文档结尾进行最终数量核对，确保处理的问题数量与源问题数量一致
      - **问题追踪**: 建立问题唯一标识符（文件名+行号+问题描述），确保每个问题都有对应的解决方案
      - **遗漏检查**: 如果发现数量不一致，必须逐一排查遗漏的问题并补充
      - **质量检查**: 验证每个解决方案的完整性和可执行性
    - **防遗漏措施**:
      - **文件清单**: 建立涉及文件的完整清单，确保每个文件的问题都被处理
      - **分类核对**: 对每个分类（绿色/黄色/红色）进行独立的数量核对
      - **交叉验证**: 通过多种方式验证问题的完整性（按文件、按类型、按行号等）
    - _输出: 完整的 out\task-import.md 文件，包含所有 Import 问题的详细分析和解决方案_

  - [x] 1.1.1 Import 问题解决方案完整性验证检查

    - **按文件维度验证策略**: 逐个文件检查 Import 问题的解决方案完整性
    - **文件级别验证流程**:

      - **步骤 1**: 从 out\问题列表.md 中提取所有包含"Import 问题"的文件清单
      - **步骤 2**: 逐个文件进行验证，对每个文件执行以下检查：

        ```markdown
        ## 对 ClassesConfig.java 的 Import 问题验证

        ### 源问题统计 (来源: out\问题列表.md)

        - Import 问题总数: 3 个
        - 具体问题: ModelLabel(行号 3,19), BaseEntity(行号 4,20), TableNameDef(行号 5)

        ### 解决方案统计 (来源: task-import.md)

        - 已处理问题: 3 个
        - 绿色标记: 2 个 (ModelLabel, BaseEntity)
        - 红色标记: 1 个 (TableNameDef 废弃)

        ### 验证结果: ✅ 数量一致，解决方案完整
        ```

    - **文件级别检查项目**:
      - **问题数量核对**: 确认该文件的 Import 问题在两个文档中数量一致
      - **问题映射核对**: 使用唯一标识（文件名+行号+类名）进行精确匹配
      - **解决方案完整性**: 确认每个 Import 问题都有对应的解决方案或归类说明
      - **解决方案质量**: 验证 import 语句的完整性和可执行性
      - **分类准确性**: 验证绿色/黄色/红色标记的正确性
    - **全局汇总验证**:
      - **文件覆盖验证**: 确认所有包含 Import 问题的文件都在 task-import.md 中有对应
      - **总数验证**: 所有文件的 Import 问题数量之和 = out\问题列表.md 中的 Import 问题总数
      - **分类统计验证**: 绿色/黄色/红色各类别的总数统计正确
    - **生成按文件维度的验证报告**:
      - 为每个文件生成详细的 Import 问题验证结果
      - 汇总全局 Import 问题处理统计
      - 如发现遗漏或不一致，提供具体的文件名、问题描述和缺失的解决方案
    - _目标: 确保每个文件的所有 Import 问题都有明确的处理方案，通过按文件维度的系统化验证防止遗漏_

  - [x] 1.1.2 修复 task-import.md 遗漏问题 (条件执行) - **已完成**

    - **执行条件**: 仅当 1.1.1 验证检查发现遗漏或不一致时才执行此任务
    - **判断标准**: 如果 1.1.1 验证报告显示"验证通过"，则跳过此任务
    - **执行内容**: 根据 1.1.1 验证报告发现的遗漏问题，修复 out\task-import.md 文件
    - 按文件逐个补充遗漏的 Import 问题解决方案
    - 确保问题数量与源文件完全匹配，无遗漏
    - **重新验证**: 修复完成后重新执行 1.1.1 验证，确保通过
    - **实际执行**: 已生成完整的 out\task-import.md 文件，包含35个文件的188个Import问题的详细分析和解决方案
    - _输出: 修复后的完整 out\task-import.md 文件_

  - [x] 1.2 废弃 API 问题分析和解决方案确定 - **重新执行完成**

    - **数据来源**: 直接读取 out\问题列表.md，，判断是否属于废弃 API 问题
    - **完整性要求**: 必须处理所有废弃 API 类型问题，不允许遗漏任何一个问题
    - **分段处理策略**:
      - **严禁一次性加载**: 绝对不要一次性加载整个 out\问题列表.md 文件
      - **分段读取**: 按文件或问题类型分段读取，每次处理 10-20 个问题
      - **逐个验证**: 每处理一个问题立即验证是否正确分类和处理
      - **实时统计**: 处理过程中实时统计已处理问题数量，确保不遗漏
    - **详细信息**: 每个问题必须包含具体的文件名、行号、废弃 API 名称和解决方案
    - **工具使用**: 由大模型来思考和使用这些工具，以及何时使用
    - **知识库查找**: 在知识库\能管代码迁移知识库.md 中查找每个废弃 API 的解决方案
    - **解决方案分类**:
      - 对有明确知识库解决方案的问题确定修复方案（绿色标记 ✅）
      - 无法找到解决方案的归类到"未识别"模块（红色标记 🔴）
    - **输出格式**: 生成详细的 out\task-abandon.md 文件，**统一按文件维度组织**：

      - **主结构**: 按文件名分组（与 out\问题列表.md 和 task-step.md 保持一致）
      - **文件内容**: 每个文件下列出该文件的所有废弃 API 问题
      - **问题详情**: 包含问题位置、解决方案分类（🟢🔴）、具体修复步骤
      - **格式示例**:

        ```markdown
        ## TeamEnergyServiceImpl.java

        ### 废弃 API 问题 1: CommonUtils.calcDouble 废弃 (🟢 绿色标记)

        - **问题位置**: 行号 94
        - **废弃原因**: CommonUtils.calcDouble 已废弃
        - **解决方案**: 使用 NumberCalcUtils.calcDouble 替代
        - **修复操作**: 将 CommonUtils.calcDouble 替换为 NumberCalcUtils.calcDouble
        ```

    - **强制完整性验证机制**:
      - **处理前验证**: 在开始处理前，先统计 out\问题列表.md 中废弃 API 问题的总数
      - **处理中验证**: 每处理完一个文件的问题，立即核对数量
      - **处理后验证**: 在文档结尾进行最终数量核对，确保处理的问题数量与源问题数量一致
      - **问题追踪**: 建立问题唯一标识符（文件名+行号+废弃 API 名称），确保每个问题都有对应的解决方案
      - **遗漏检查**: 如果发现数量不一致，必须逐一排查遗漏的问题并补充
    - _输出: 完整的 out\task-abandon.md 文件，包含所有废弃 API 问题的详细分析和解决方案_

  - [x] 1.2.1 废弃 API 问题解决方案完整性验证检查 - **重新执行完成**

    - **按文件维度验证策略**: 逐个文件检查废弃 API 问题的解决方案完整性
    - **文件级别验证流程**:

      - **步骤 1**: 从 out\问题列表.md 中提取所有包含"废弃 API 问题"的文件清单
      - **步骤 2**: 逐个文件进行验证，对每个文件执行以下检查：

        ```markdown
        ## 对 TeamEnergyServiceImpl.java 的废弃 API 问题验证

        ### 源问题统计 (来源: out\问题列表.md)

        - 废弃 API 问题总数: 8 个
        - 具体问题: CommonUtils.calcDouble(行号 94,116,118,261,351,354,414,416)

        ### 解决方案统计 (来源: task-abandon.md)

        - 已处理问题: 8 个
        - 绿色标记: 8 个 (使用 NumberCalcUtils.calcDouble 替代)

        ### 验证结果: ✅ 数量一致，解决方案完整
        ```

    - **文件级别检查项目**:
      - **问题数量核对**: 确认该文件的废弃 API 问题在两个文档中数量一致
      - **问题映射核对**: 使用唯一标识（文件名+行号+废弃 API 名称）进行精确匹配
      - **解决方案完整性**: 确认每个废弃 API 问题都有对应的知识库解决方案或归类说明
      - **知识库匹配验证**: 验证知识库匹配的准确性和解决方案的适用性
      - **分类准确性**: 验证绿色/红色标记的正确性
    - **全局汇总验证**:
      - **文件覆盖验证**: 确认所有包含废弃 API 问题的文件都在 task-abandon.md 中有对应
      - **总数验证**: 所有文件的废弃 API 问题数量之和 = out\问题列表.md 中的废弃 API 问题总数
      - **分类统计验证**: 绿色/红色各类别的总数统计正确
    - **生成按文件维度的验证报告**:
      - 为每个文件生成详细的废弃 API 问题验证结果
      - 汇总全局废弃 API 问题处理统计
      - 如发现遗漏或不一致，提供具体的文件名、API 名称和缺失的解决方案
    - _目标: 确保每个文件的所有废弃 API 问题都有明确的处理方案，通过按文件维度的系统化验证防止遗漏_

  - [-] 1.2.2 修复 task-abandon.md 遗漏问题 (条件执行) - **跳过执行 - 验证通过**

    - **执行条件**: 仅当 1.2.1 验证检查发现遗漏或不一致时才执行此任务
    - **判断标准**: 如果 1.2.1 验证报告显示"验证通过"，则跳过此任务
    - **执行内容**: 根据 1.2.1 验证报告发现的遗漏问题，修复 out\task-abandon.md 文件
    - 按文件逐个补充遗漏的废弃 API 问题解决方案
    - 确保问题数量与源文件完全匹配，无遗漏
    - **重新验证**: 修复完成后重新执行 1.2.1 验证，确保通过
    - _输出: 修复后的完整 out\task-abandon.md 文件_

  - [x] 1.3 消息推送变更问题分析和解决方案确定 - **已完成**
    - **数据来源**: 直接读取 out\问题列表.md，处理消息推送变更相关的问题
    - **问题范围**: 基于知识库第 3 类"消息推送变更"，包括：
      - MessagePushUtils 完全废弃问题
      - messagePushUtils.pushToWeb 方法调用问题
      - 新的 WebNotification 服务替换方案
    - **完整性要求**: 必须处理所有消息推送变更相关的问题，不允许遗漏任何一个问题
    - **分段处理策略**:
      - **严禁一次性加载**: 绝对不要一次性加载整个 out\问题列表.md 文件
      - **分段读取**: 按问题类型分段读取，每次处理 10-20 个问题
      - **逐个验证**: 每处理一个问题立即验证是否正确分类和处理
      - **实时统计**: 处理过程中实时统计已处理问题数量，确保不遗漏
    - **详细信息**: 每个问题必须包含具体的文件名、行号、问题类型和解决方案
    - **工具使用**: 由大模型来思考和使用这些工具，以及何时使用
    - **知识库查找**: 基于知识库第 3 类"消息推送变更"的解决方案
    - **解决方案分类**:
      - 对有明确知识库解决方案的问题确定修复方案（绿色标记 ✅）
      - 无法确定解决方案的归类到"未识别"模块（红色标记 🔴）
    - **输出格式**: 生成详细的 out\task-message.md 文件，**统一按文件维度组织**
    - **强制完整性验证机制**: 同前述验证机制
    - **实际执行结果**: 经过全面搜索分析，当前代码库中未发现任何MessagePushUtils相关的消息推送问题
    - **处理统计**: 搜索了全部2334行259个问题，未发现相关问题需要处理
    - **验证方式**: 使用正则表达式对MessagePushUtils、pushToWeb、WebNotification等关键词进行全文搜索
    - _输出: 完整的 out\task-message.md 文件，说明当前代码库无消息推送变更问题需要处理_

  - [x] 1.3.1 消息推送变更问题解决方案完整性验证检查 - **已完成**

    - **按文件维度验证策略**: 逐个文件检查消息推送变更问题的解决方案完整性
    - **文件级别验证流程**:
      - **步骤 1**: 从 out\问题列表.md 中提取所有包含"消息推送变更问题"的文件清单
      - **步骤 2**: 逐个文件进行验证，对每个文件执行以下检查
    - **文件级别检查项目**:
      - **问题数量核对**: 确认该文件的消息推送变更问题在两个文档中数量一致
      - **问题映射核对**: 使用唯一标识（文件名+行号+问题类型）进行精确匹配
      - **解决方案完整性**: 确认每个消息推送变更问题都有对应的知识库解决方案或归类说明
      - **知识库匹配验证**: 验证知识库匹配的准确性和解决方案的适用性
      - **分类准确性**: 验证绿色/红色标记的正确性
    - **全局汇总验证**:
      - **文件覆盖验证**: 确认所有包含消息推送变更问题的文件都在 task-message.md 中有对应
      - **总数验证**: 所有文件的消息推送变更问题数量之和 = out\问题列表.md 中的消息推送变更问题总数
      - **分类统计验证**: 绿色/红色各类别的总数统计正确
    - **生成按文件维度的验证报告**:
      - 为每个文件生成详细的消息推送变更问题验证结果
      - 汇总全局消息推送变更问题处理统计
      - 如发现遗漏或不一致，提供具体的文件名、问题类型和缺失的解决方案
    - **实际执行结果**: 验证通过 - 源问题文件中无消息推送变更相关问题，task-message.md正确反映了这一情况
    - **验证统计**: 搜索了全部2334行259个问题，确认0个消息推送相关问题，处理数量完全一致
    - **验证方法**: 使用正则表达式对所有相关关键词进行全文搜索验证
    - _目标: 确保每个文件的所有消息推送变更问题都有明确的处理方案，通过按文件维度的系统化验证防止遗漏_

  - [-] 1.3.2 修复 task-message.md 遗漏问题 (条件执行) - **跳过执行 - 验证通过**

    - **执行条件**: 仅当 1.3.1 验证检查发现遗漏或不一致时才执行此任务
    - **判断标准**: 如果 1.3.1 验证报告显示"验证通过"，则跳过此任务
    - **执行内容**: 根据 1.3.1 验证报告发现的遗漏问题，修复 out\task-message.md 文件
    - 按文件逐个补充遗漏的消息推送变更问题解决方案
    - 确保问题数量与源文件完全匹配，无遗漏
    - **重新验证**: 修复完成后重新执行 1.3.1 验证，确保通过
    - **跳过原因**: 1.3.1验证检查显示"验证通过"，根据判断标准自动跳过此任务
    - **验证依据**: task-message-verification.md显示验证完全通过，无遗漏或不一致
    - **当前状态**: task-message.md文件完整准确，无需修复
    - _输出: 无需修复，保持现有的完整 out\task-message.md 文件_

  - [x] 1.4 权限 ID 调整问题分析和解决方案确定 - **已完成**

    - **数据来源**: 直接读取 out\问题识别.md，处理权限 ID 调整相关的问题
    - **问题范围**: 基于知识库第 4 类"权限 ID 调整"，包括：
      - @OperationLog(operationType 相关的权限 ID 问题
      - 权限 ID 需要在 10000-20000 之间的调整
      - 常量类中权限 ID 值的修改
    - **完整性要求**: 必须处理所有权限 ID 调整相关的问题，不允许遗漏任何一个问题
    - **分段处理策略**:
      - **严禁一次性加载**: 绝对不要一次性加载整个 out\问题列表.md 文件
      - **分段读取**: 按文件或问题类型分段读取，每次处理 10-20 个问题
      - **逐个验证**: 每处理一个问题立即验证是否正确分类和处理
      - **实时统计**: 处理过程中实时统计已处理问题数量，确保不遗漏
    - **详细信息**: 每个问题必须包含具体的文件名、行号、问题类型和解决方案
    - **工具使用**: 由大模型来思考和使用这些工具，以及何时使用
    - **知识库查找**: 基于知识库第 4 类"权限 ID 调整"的解决方案
    - **解决方案分类**:
      - 对有明确知识库解决方案的问题确定修复方案（绿色标记 ✅）
      - 无法确定解决方案的归类到"未识别"模块（红色标记 🔴）
    - **输出格式**: 生成详细的 out\task-permission.md 文件，**统一按文件维度组织**
    - **强制完整性验证机制**: 同前述验证机制
    - **实际执行结果**: 成功识别并处理了9个权限ID调整问题，全部来自TeamConfigController类
    - **处理统计**: 9个问题全部标记为绿色（有明确知识库解决方案），涉及4个常量值调整
    - **解决方案**: 基于知识库将所有权限ID常量值加10000偏移量，调整到10000-20000范围内
    - _输出: 完整的 out\task-permission.md 文件，包含所有权限 ID 调整问题的详细分析和解决方案_

  - [x] 1.4.1 权限 ID 调整问题解决方案完整性验证检查 - **已完成**

    - **按文件维度验证策略**: 逐个文件检查权限 ID 调整问题的解决方案完整性
    - **文件级别验证流程**:
      - **步骤 1**: 从 out\问题识别.md 中提取所有包含"权限 ID 调整问题"的文件清单
      - **步骤 2**: 逐个文件进行验证，对每个文件执行以下检查
    - **文件级别检查项目**:
      - **问题数量核对**: 确认该文件的权限 ID 调整问题在两个文档中数量一致
      - **问题映射核对**: 使用唯一标识（文件名+行号+问题类型）进行精确匹配
      - **解决方案完整性**: 确认每个权限 ID 调整问题都有对应的知识库解决方案或归类说明
      - **知识库匹配验证**: 验证知识库匹配的准确性和解决方案的适用性
      - **分类准确性**: 验证绿色/红色标记的正确性
    - **全局汇总验证**:
      - **文件覆盖验证**: 确认所有包含权限 ID 调整问题的文件都在 task-permission.md 中有对应
      - **总数验证**: 所有文件的权限 ID 调整问题数量之和 = out\问题识别.md 中的权限 ID 调整问题总数
      - **分类统计验证**: 绿色/红色各类别的总数统计正确
    - **生成按文件维度的验证报告**:
      - 为每个文件生成详细的权限 ID 调整问题验证结果
      - 汇总全局权限 ID 调整问题处理统计
      - 如发现遗漏或不一致，提供具体的文件名、问题类型和缺失的解决方案
    - **实际执行结果**: 验证完全通过 - 9个权限ID调整问题全部有对应解决方案，无遗漏
    - **验证统计**: 源问题9个，处理问题9个，匹配率100%，全部标记为绿色
    - **验证方法**: 逐一对比源问题与解决方案，验证行号、方法名、文件名完全匹配
    - _目标: 确保每个文件的所有权限 ID 调整问题都有明确的处理方案，通过按文件维度的系统化验证防止遗漏_

  - [-] 1.4.2 修复 task-permission.md 遗漏问题 (条件执行) - **跳过执行 - 验证通过**

    - **执行条件**: 仅当 1.4.1 验证检查发现遗漏或不一致时才执行此任务
    - **判断标准**: 如果 1.4.1 验证报告显示"验证通过"，则跳过此任务
    - **执行内容**: 根据 1.4.1 验证报告发现的遗漏问题，修复 out\task-permission.md 文件
    - 按文件逐个补充遗漏的权限 ID 调整问题解决方案
    - 确保问题数量与源文件完全匹配，无遗漏
    - **重新验证**: 修复完成后重新执行 1.4.1 验证，确保通过
    - **跳过原因**: 1.4.1验证检查显示"验证完全通过"，根据判断标准自动跳过此任务
    - **验证依据**: task-permission-verification.md显示100%验证通过，无遗漏或不一致
    - **当前状态**: task-permission.md文件完整准确，无需修复
    - _输出: 无需修复，保持现有的完整 out\task-permission.md 文件_

  - [x] 1.5 单位服务变更问题分析和解决方案确定 - **已完成**

    - **数据来源**: 直接读取 out\问题识别.md，处理单位服务变更相关的问题
    - **问题范围**: 基于知识库第 5 类"单位服务变更"，包括：
      - UnitService → EnergyUnitService 的包和类名变更
      - UserDefineUnit → UserDefineUnitDTO 的实体变更
      - getUnit 方法 → queryUnitCoef 方法的签名变更
      - 业务规则适配（能耗、产量分开查询）
    - **完整性要求**: 必须处理所有单位服务变更相关的问题，不允许遗漏任何一个问题
    - **分段处理策略**:
      - **严禁一次性加载**: 绝对不要一次性加载整个 out\问题识别.md 文件
      - **分段读取**: 按文件或问题类型分段读取，每次处理 10-20 个问题
      - **逐个验证**: 每处理一个问题立即验证是否正确分类和处理
      - **实时统计**: 处理过程中实时统计已处理问题数量，确保不遗漏
    - **详细信息**: 每个问题必须包含具体的文件名、行号、问题类型和解决方案
    - **工具使用**: 由大模型来思考和使用这些工具，以及何时使用
    - **知识库查找**: 基于知识库第 5 类"单位服务变更"的解决方案
    - **解决方案分类**:
      - 对有明确知识库解决方案的问题确定修复方案（绿色标记 ✅）
      - 无法确定解决方案的归类到"未识别"模块（红色标记 🔴）
    - **输出格式**: 生成详细的 out\task-unit.md 文件，**统一按文件维度组织**
    - **强制完整性验证机制**: 同前述验证机制
    - **实际执行结果**: 成功识别并处理了4个单位服务变更问题，全部来自TeamEnergyServiceImpl类
    - **处理统计**: 4个问题全部标记为绿色（有明确知识库解决方案），涉及服务类变更、实体变更、方法签名变更
    - **解决方案**: 基于知识库提供了完整的UnitService→EnergyUnitService迁移指导，包括方法调用和参数结构变更
    - _输出: 完整的 out\task-unit.md 文件，包含所有单位服务变更问题的详细分析和解决方案_

  - [ ] 1.5.1 单位服务变更问题解决方案完整性验证检查

    - **按文件维度验证策略**: 逐个文件检查单位服务变更问题的解决方案完整性
    - **文件级别验证流程**:
      - **步骤 1**: 从 out\问题列表.md 中提取所有包含"单位服务变更问题"的文件清单
      - **步骤 2**: 逐个文件进行验证，对每个文件执行以下检查
    - **文件级别检查项目**:
      - **问题数量核对**: 确认该文件的单位服务变更问题在两个文档中数量一致
      - **问题映射核对**: 使用唯一标识（文件名+行号+问题类型）进行精确匹配
      - **解决方案完整性**: 确认每个单位服务变更问题都有对应的知识库解决方案或归类说明
      - **知识库匹配验证**: 验证知识库匹配的准确性和解决方案的适用性
      - **分类准确性**: 验证绿色/红色标记的正确性
    - **全局汇总验证**:
      - **文件覆盖验证**: 确认所有包含单位服务变更问题的文件都在 task-unit.md 中有对应
      - **总数验证**: 所有文件的单位服务变更问题数量之和 = out\问题列表.md 中的单位服务变更问题总数
      - **分类统计验证**: 绿色/红色各类别的总数统计正确
    - **生成按文件维度的验证报告**:
      - 为每个文件生成详细的单位服务变更问题验证结果
      - 汇总全局单位服务变更问题处理统计
      - 如发现遗漏或不一致，提供具体的文件名、问题类型和缺失的解决方案
    - _目标: 确保每个文件的所有单位服务变更问题都有明确的处理方案，通过按文件维度的系统化验证防止遗漏_

  - [ ] 1.5.2 修复 task-unit.md 遗漏问题 (条件执行)

    - **执行条件**: 仅当 1.5.1 验证检查发现遗漏或不一致时才执行此任务
    - **判断标准**: 如果 1.5.1 验证报告显示"验证通过"，则跳过此任务
    - **执行内容**: 根据 1.5.1 验证报告发现的遗漏问题，修复 out\task-unit.md 文件
    - 按文件逐个补充遗漏的单位服务变更问题解决方案
    - 确保问题数量与源文件完全匹配，无遗漏
    - **重新验证**: 修复完成后重新执行 1.5.1 验证，确保通过
    - _输出: 修复后的完整 out\task-unit.md 文件_

  - [ ] 1.6 物理量查询服务问题分析和解决方案确定

    - **数据来源**: 直接读取 out\问题列表.md，处理物理量查询服务相关的问题
    - **问题范围**: 基于知识库第 6 类"物理量查询服务"，包括：
      - QuantityObjectService 相关问题
      - QuantityObjectMapService 相关问题
      - QuantityObjectDataService 相关问题
      - 需要依赖 eem-base-fusion-energy-sdk 的问题
    - **完整性要求**: 必须处理所有物理量查询服务相关的问题，不允许遗漏任何一个问题
    - **分段处理策略**:
      - **严禁一次性加载**: 绝对不要一次性加载整个 out\问题列表.md 文件
      - **分段读取**: 按文件或问题类型分段读取，每次处理 10-20 个问题
      - **逐个验证**: 每处理一个问题立即验证是否正确分类和处理
      - **实时统计**: 处理过程中实时统计已处理问题数量，确保不遗漏
    - **详细信息**: 每个问题必须包含具体的文件名、行号、问题类型和解决方案
    - **工具使用**: 由大模型来思考和使用这些工具，以及何时使用
    - **知识库查找**: 基于知识库第 6 类"物理量查询服务"的解决方案
    - **解决方案分类**:
      - 对有明确知识库解决方案的问题确定修复方案（绿色标记 ✅）
      - 无法确定解决方案的归类到"未识别"模块（红色标记 🔴）
    - **输出格式**: 生成详细的 out\task-quantity.md 文件，**统一按文件维度组织**
    - **强制完整性验证机制**: 同前述验证机制
    - _输出: 完整的 out\task-quantity.md 文件，包含所有物理量查询服务问题的详细分析和解决方案_

  - [ ] 1.6.1 物理量查询服务问题解决方案完整性验证检查

    - **按文件维度验证策略**: 逐个文件检查物理量查询服务问题的解决方案完整性
    - **文件级别验证流程**:
      - **步骤 1**: 从 out\问题列表.md 中提取所有包含"物理量查询服务问题"的文件清单
      - **步骤 2**: 逐个文件进行验证，对每个文件执行以下检查
    - **文件级别检查项目**:
      - **问题数量核对**: 确认该文件的物理量查询服务问题在两个文档中数量一致
      - **问题映射核对**: 使用唯一标识（文件名+行号+问题类型）进行精确匹配
      - **解决方案完整性**: 确认每个物理量查询服务问题都有对应的知识库解决方案或归类说明
      - **知识库匹配验证**: 验证知识库匹配的准确性和解决方案的适用性
      - **分类准确性**: 验证绿色/红色标记的正确性
    - **全局汇总验证**:
      - **文件覆盖验证**: 确认所有包含物理量查询服务问题的文件都在 task-quantity.md 中有对应
      - **总数验证**: 所有文件的物理量查询服务问题数量之和 = out\问题列表.md 中的物理量查询服务问题总数
      - **分类统计验证**: 绿色/红色各类别的总数统计正确
    - **生成按文件维度的验证报告**:
      - 为每个文件生成详细的物理量查询服务问题验证结果
      - 汇总全局物理量查询服务问题处理统计
      - 如发现遗漏或不一致，提供具体的文件名、问题类型和缺失的解决方案
    - _目标: 确保每个文件的所有物理量查询服务问题都有明确的处理方案，通过按文件维度的系统化验证防止遗漏_

  - [ ] 1.6.2 修复 task-quantity.md 遗漏问题 (条件执行)

    - **执行条件**: 仅当 1.6.1 验证检查发现遗漏或不一致时才执行此任务
    - **判断标准**: 如果 1.6.1 验证报告显示"验证通过"，则跳过此任务
    - **执行内容**: 根据 1.6.1 验证报告发现的遗漏问题，修复 out\task-quantity.md 文件
    - 按文件逐个补充遗漏的物理量查询服务问题解决方案
    - 确保问题数量与源文件完全匹配，无遗漏
    - **重新验证**: 修复完成后重新执行 1.6.1 验证，确保通过
    - _输出: 修复后的完整 out\task-quantity.md 文件_

  - [ ] 1.7 其他类型问题分析和解决方案确定

    - **数据来源**: 直接读取 out\问题列表.md，处理不在知识库中的其他类型问题
    - **问题范围**: 处理除 1.1-1.6 任务范围外的所有其他问题，包括：
      - 不在知识库 7 类问题中的编译错误
      - 新出现的未知问题类型
      - 复杂的架构层面问题
      - 业务逻辑相关的兼容性问题
      - 第三方依赖相关问题
      - 配置文件相关问题
    - **完整性要求**: 必须处理所有其他类型问题，确保 out\问题列表.md 中的所有问题都被覆盖，不允许遗漏任何一个问题
    - **分段处理策略**:
      - **严禁一次性加载**: 绝对不要一次性加载整个 out\问题列表.md 文件
      - **分段读取**: 按文件或问题类型分段读取，每次处理 10-20 个问题
      - **逐个验证**: 每处理一个问题立即验证是否正确分类和处理
      - **实时统计**: 处理过程中实时统计已处理问题数量，确保不遗漏
    - **详细信息**: 每个问题必须包含具体的文件名、行号、问题类型和解决方案
    - **工具使用**: 由大模型来思考和使用这些工具，以及何时使用
    - **问题识别策略**:
      - 首先排除已在 1.1-1.6 任务中处理的问题
      - 对剩余问题进行分类和分析
      - 尝试基于编程最佳实践和通用解决方案确定修复方案
    - **解决方案分类**:
      - 对能够确定解决方案的问题提供修复方案（黄色标记 🟡）
      - 无法确定解决方案的归类到"未识别"模块（红色标记 🔴）
    - **输出格式**: 生成详细的 out\task-other.md 文件，**统一按文件维度组织**
    - **强制完整性验证机制**: 同前述验证机制
    - **全覆盖验证**: 确保 1.1-1.7 所有任务处理的问题总数等于 out\问题列表.md 中的问题总数
    - _输出: 完整的 out\task-other.md 文件，包含所有其他类型问题的详细分析和解决方案_

  - [ ] 1.7.1 其他类型问题解决方案完整性验证检查

    - **按文件维度验证策略**: 逐个文件检查其他类型问题的解决方案完整性
    - **文件级别验证流程**:
      - **步骤 1**: 从 out\问题列表.md 中提取所有未被 1.1-1.6 任务处理的问题文件清单
      - **步骤 2**: 逐个文件进行验证，对每个文件执行以下检查
    - **文件级别检查项目**:
      - **问题数量核对**: 确认该文件的其他类型问题在两个文档中数量一致
      - **问题映射核对**: 使用唯一标识（文件名+行号+问题类型）进行精确匹配
      - **解决方案完整性**: 确认每个其他问题都有对应的解决方案、分析思路或归类说明
      - **分类准确性**: 验证黄色/橙色/红色标记的正确性
      - **排除重复**: 确认没有与 1.1-1.6 任务重复处理的问题
    - **全局汇总验证**:
      - **文件覆盖验证**: 确认所有包含其他类型问题的文件都在 task-other.md 中有对应
      - **总数验证**: 所有文件的其他类型问题数量之和 = out\问题列表.md 中的其他类型问题总数
      - **全覆盖验证**: 验证 1.1-1.7 所有任务处理的问题总数 = out\问题列表.md 中的问题总数
      - **分类统计验证**: 各类别的总数统计正确
    - **生成按文件维度的验证报告**:
      - 为每个文件生成详细的其他问题验证结果
      - 汇总全局其他问题处理统计
      - 汇总 1.1-1.7 所有任务的问题处理统计，确保 100%覆盖
      - 如发现遗漏或不一致，提供具体的文件名、问题类型和缺失的解决方案
    - _目标: 确保每个文件的所有其他类型问题都有明确的处理方案，并验证所有问题都被完整覆盖_

  - [ ] 1.7.2 修复 task-other.md 遗漏问题 (条件执行)

    - **执行条件**: 仅当 1.7.1 验证检查发现遗漏或不一致时才执行此任务
    - **判断标准**: 如果 1.7.1 验证报告显示"验证通过"，则跳过此任务
    - **执行内容**: 根据 1.7.1 验证报告发现的遗漏问题，修复 out\task-other.md 文件
    - 按文件逐个补充遗漏的其他类型问题解决方案
    - 确保问题数量与源文件完全匹配，无遗漏
    - 确保 1.1-1.7 所有任务的问题总数等于源问题总数
    - **重新验证**: 修复完成后重新执行 1.7.1 验证，确保通过
    - _输出: 修复后的完整 out\task-other.md 文件_

- [x] 2. 生成按文件组织的详细修复任务

  - [x] 2.1 按文件优先级排序和组织

    - 读取所有第一轮处理的任务文件整理问题和解决方案：
      - out\task-import.md（Import 问题解决方案）
      - out\task-abandon.md（废弃 API 问题解决方案）
      - out\task-message.md（消息推送变更问题解决方案）
      - out\task-permission.md（权限 ID 调整问题解决方案）
      - out\task-unit.md（单位服务变更问题解决方案）
      - out\task-quantity.md（物理量查询服务问题解决方案）
      - out\task-other.md（其他类型问题解决方案）
    - 由大模型来思考和使用这些工具，以及何时使用
    - 按照常量类、实体类、工具类、DAO、Service、Controller 的顺序排列
    - 采用从底层到上层的修复策略
    - 识别文件间的依赖关系
    - 确定修复的先后顺序
    - _输出: 优先级排序的文件列表_

  - [x] 2.2 生成详细的 task-step.md 修复任务文件

    - **来源文件整合**: 将所有已确定的解决方案按文件和优先级组织，必须包含：
      - out\task-import.md 中的所有 Import 问题解决方案
      - out\task-abandon.md 中的所有废弃 API 问题解决方案
      - out\task-message.md 中的所有消息推送变更问题解决方案
      - out\task-permission.md 中的所有权限 ID 调整问题解决方案
      - out\task-unit.md 中的所有单位服务变更问题解决方案
      - out\task-quantity.md 中的所有物理量查询服务问题解决方案
      - out\task-other.md 中的所有其他类型问题解决方案
    - **任务格式要求**: 生成的 task-step.md 必须使用 checkbox 格式，**按文件维度组织**：
      - **主结构**: 按文件名分组，与前面所有输出文件保持一致的文件维度组织
      - **文件任务**: 每个文件是一个主任务 `- [ ] 修复 ClassesConfig.java 的所有问题`
      - **子任务**: 每个具体问题是一个子任务 `- [ ] 2.1 添加 ModelLabel 导入 (行号: 3, 19)`
      - **格式示例**:
        ```markdown
        - [ ] 2. 修复 ClassesConfig.java 的 Import 和继承问题
          - [ ] 2.1 添加 ModelLabel 导入 (行号: 3, 19)
          - [ ] 2.2 修复继承关系变更 (行号: 20)
        ```
    - **详细步骤生成**: 为每个文件的每个问题生成具体的修复步骤：
      - 问题描述和位置（具体行号）
      - 具体的修复操作（替换内容、添加代码等）
      - 修复后的验证方法
      - 预期结果
    - **优先级排序**: 按照常量类 → 实体类 → 工具类 → DAO → Service → Controller 的顺序组织
    - **未识别问题**: 将未识别的问题单独分类，放在任务列表最后
    - **可执行性**: 每个子任务包含完整的执行指令，无需额外分析
    - **状态管理**: 生成的任务文件应该支持：
      - 每个任务可以独立执行和完成
      - 状态可以通过 checkbox 进行跟踪
      - 支持部分完成和增量处理
    - **完整性验证机制**:
      - 在文档开头记录各来源文件的问题总数
      - 在文档结尾进行数量核对，确保所有问题都转换为修复步骤
      - 按文件分组统计，确保每个文件的问题都有对应的修复步骤
    - **格式一致性验证**:
      - **文件维度一致性**: 确保与 out\问题列表.md 的文件顺序和分组保持一致
      - **问题映射一致性**: 确保每个问题在各个文件中的描述和位置信息一致
      - **结构层次一致性**: 确保文件 → 问题 → 子任务的层次结构清晰且一致
      - **未识别问题处理**：未识别的问题放在最后，先不处理
    - _输出: 完整的 out\task-step.md 文件，包含所有可执行的具体修复子任务，每个任务都有独立的 checkbox 状态_

  - [x] 2.3 task-step.md 完整性验证检查

    - **按文件维度验证策略**: 逐个文件检查 task-step.md 中的修复任务完整性
    - **文件级别验证流程**:

      - **步骤 1**: 从各来源文件（task-import.md、task-abandon.md、task-other.md）中提取所有文件的问题清单
      - **步骤 2**: 逐个文件进行验证，对每个文件执行以下检查：

        ```markdown
        ## 对 ClassesConfig.java 的修复任务验证

        ### 来源问题统计

        - task-import.md: 3 个 Import 问题
        - task-abandon.md: 0 个废弃 API 问题
        - task-other.md: 1 个继承关系变更问题
        - 总计: 4 个问题

        ### 修复任务统计 (来源: task-step.md)

        - 主任务: 1 个 (修复 ClassesConfig.java 的所有问题)
        - 子任务: 4 个 (2.1-2.4 对应各个具体问题)
        - 总计: 4 个修复步骤

        ### 验证结果: ✅ 数量一致，任务完整
        ```

    - **文件级别检查项目**:
      - **问题数量核对**: 确认该文件在来源文件中的问题总数与 task-step.md 中的修复步骤数量一致
      - **问题映射核对**: 使用唯一标识（文件名+行号+问题描述）确保每个源问题都有对应的修复步骤
      - **任务完整性**: 检查每个问题是否都转换为了具体的可执行修复步骤
      - **格式正确性**: 验证该文件的任务是否使用了正确的 checkbox 格式和层级结构
      - **内容质量**: 验证修复步骤是否包含问题描述、位置、修复操作、验证方法
    - **全局汇总验证**:
      - **文件覆盖验证**: 确认所有来源文件中涉及的文件都在 task-step.md 中有对应的修复任务
      - **总数验证**: 所有文件的修复步骤数量之和 = 各来源文件的问题总数之和
      - **优先级排序验证**: 确认文件优先级排序正确（常量类 → 实体类 → 工具类 → DAO → Service → Controller）
    - **生成按文件维度的验证报告**:
      - 为每个文件生成详细的修复任务验证结果
      - 汇总全局修复任务统计信息
      - 如发现遗漏或不一致，提供具体的文件名、问题描述和缺失的修复步骤
    - **状态管理验证**: 确认生成的任务文件支持独立的状态跟踪和增量处理
    - _目标: 确保每个文件的所有问题都转换为可执行的修复任务，通过按文件维度的系统化验证防止遗漏_

## 阶段 3: 执行修复任务

- [-] 7. 按 out\task-step.md 逐个执行第一轮修复

  - [x] 7.1 执行第一轮修复任务

    - 按照生成的 out\task-step.md 文件中的子任务顺序逐一执行
    - 对每个子任务应用已确定的具体修复方案
    - 验证每个修复的正确性（编译通过、功能正常）
    - 使用 git-commit-helper.ps1 提交每个子任务的修改
    - **状态更新**: 在 out\task-step.md 中将完成的子任务状态从 `- [ ]` 更新为 `- [x]`
    - **独立处理**: 每个任务可以独立执行，支持部分完成和增量处理
    - **进度跟踪**: 通过 checkbox 状态可以清晰看到整体修复进度
    - _执行原则: 严格按照 out\task-step.md 的顺序和方案执行，支持任务级别的状态管理_

  - [ ] 7.2 处理第一轮未识别问题
    - 所有已识别问题修复完成后，处理"未识别"类别的问题
    - 对未识别问题进行进一步研究和分析
    - 尝试找到解决方案或制定临时处理策略
    - 记录处理结果和遗留问题
    - _处理原则: 放在最后处理，不影响主要修复进度_

- [ ] 8. 按 out\task-step-round2.md 逐个执行第二轮修复

  - [ ] 8.1 执行第二轮修复任务

    - **执行条件**: 必须等第一轮所有修复任务完成并编译通过后才能开始
    - 按照生成的 out\task-step-round2.md 文件中的子任务顺序逐一执行
    - 对每个子任务应用已确定的具体修复方案
    - 验证每个修复的正确性（编译通过、功能正常）
    - 使用 git-commit-helper.ps1 提交每个子任务的修改
    - **状态更新**: 在 out\task-step-round2.md 中将完成的子任务状态从 `- [ ]` 更新为 `- [x]`
    - _执行原则: 严格按照 out\task-step-round2.md 的顺序和方案执行_

  - [ ] 8.2 处理第二轮未识别问题
    - 所有第二轮已识别问题修复完成后，处理"未识别"类别的问题
    - 对未识别问题进行进一步研究和分析
    - 尝试找到解决方案或制定临时处理策略
    - 记录处理结果和遗留问题
    - _处理原则: 放在最后处理，不影响主要修复进度_

## 阶段 4: 验证和完善

- [ ] 9. 修复验证和测试

  - [ ] 9.1 编译验证

    - 确保所有修复后的代码能够正常编译
    - 解决修复过程中引入的新问题
    - 验证依赖关系的正确性
    - 生成最终的编译报告
    - _目标: 实现零编译错误_

  - [ ] 9.2 功能测试验证
    - 对修复的关键功能进行测试
    - 验证业务逻辑的正确性
    - 检查性能和稳定性影响
    - 记录测试结果和问题
    - _目标: 确保功能正常_

- [ ] 10. 文档和总结

  - [ ] 10.1 生成修复报告

    - 统计修复的问题数量和类型
    - 记录修复过程中的关键决策
    - 整理遗留问题和风险点
    - 生成完整的修复报告
    - _输出: 迁移修复总结报告_

  - [ ] 10.2 更新项目文档
    - 更新项目的技术文档
    - 记录重要的架构变更
    - 更新部署和运维指南
    - 提供后续维护建议
    - _输出: 更新的项目文档_

## 🚨 重要执行原则和防遗漏措施

### ⚠️ 关键执行要求

**基于实际经验教训，以下原则必须严格遵守：**

1. **严禁一次性加载大文件**

   - 绝对不要一次性读取整个 `out\questionlist.md` 文件
   - 该文件包含 247 个问题，内容庞大，一次性加载会导致内容截断和遗漏
   - 必须采用分段读取策略，每次处理 10-20 个问题

2. **强制分段处理机制**

   - 按文件名、问题类型或行号范围分段读取
   - 每处理完一段立即进行数量验证
   - 建立处理进度跟踪，确保覆盖所有问题

3. **实时验证和追踪**

   - 处理前：统计源问题总数
   - 处理中：实时统计已处理问题数量
   - 处理后：进行最终数量核对
   - 建立问题唯一标识符进行精确追踪

4. **多重验证机制**

   - 数量验证：确保处理数量与源数量一致
   - 映射验证：确保每个问题都有对应解决方案
   - 文件覆盖验证：确保所有涉及文件都被处理
   - 质量验证：确保解决方案的完整性和可执行性

5. **遗漏检查和补救**

   - 如发现数量不一致，必须立即停止并排查
   - 逐一对比源文件和输出文件，找出遗漏问题
   - 补充遗漏问题后重新进行完整性验证

6. **格式一致性要求**

   - **统一按文件维度组织**: 所有输出文件（questionlist.md、task-import.md、task-abandon.md、task-other.md、task-step.md）都必须按文件维度组织
   - **保持文件顺序一致**: 各文件中的文件顺序必须与 out\问题列表.md 保持一致
   - **问题描述一致**: 同一个问题在不同文件中的描述、行号、位置信息必须完全一致
   - **便于追溯和处理**: 确保从原始问题到最终任务的完整追溯链路

7. **条件执行机制**
   - **智能跳过**: 验证通过的任务自动跳过对应的修复任务，提高执行效率
   - **按需修复**: 只有发现问题时才执行修复，避免不必要的操作
   - **重新验证**: 修复完成后必须重新验证，确保问题得到解决
   - **状态追踪**: 清晰记录每个验证和修复任务的执行状态

## 执行原则

### 🔄 迭代执行流程

1. **按序执行**: 严格按照 out\task-step.md 中的子任务顺序逐一执行
2. **条件判断**: 对于标记为"条件执行"的任务，先检查前置验证结果
   - 如果验证通过，跳过修复任务
   - 如果验证不通过，执行修复任务并重新验证
3. **应用方案**: 直接应用每个子任务中已确定的具体修复方案
4. **验证结果**: 验证修复后的编译和功能正确性
5. **提交修改**: 使用 git-commit-helper.ps1 提交每个子任务的修改
6. **更新状态**: 在 out\task-step.md 中将完成的子任务从 `- [ ]` 更新为 `- [x]`
7. **独立处理**: 每个任务可以独立执行，支持部分完成和增量处理
8. **进度跟踪**: 通过 checkbox 状态清晰跟踪整体修复进度
9. **处理未识别**: 所有已识别问题修复完成后，最后处理未识别问题

### 🎯 优先级标记说明

- **🟢 绿色**: 确定性修复，可直接执行
  - 单一匹配的 import 问题
  - 知识库有明确解决方案的废弃 API 问题
- **🟡 黄色**: 需要 AI 智能判断或测试验证
  - 多候选匹配的 import 问题（需要 class_file_reader.py 分析）
  - 方法签名变更和配置更新问题
- **⚪ 未识别**: 无法确定解决方案，需要进一步研究
  - AI 无法判断最佳匹配的 import 问题
  - 知识库没有解决方案的废弃 API 问题
  - 复杂的架构层面问题

### 📋 统一输出格式说明

**所有输出文件统一采用按文件维度组织的格式，确保一致性和处理效率：**

#### 1. **out\问题列表.md** (原始问题，按文件维度)

```markdown
## ClassesConfig.java

### 问题 1

error_code: "import_issues"
missing_class: "ModelLabel"
line: [3, 19]
```

#### 2. **out\task-import.md** (Import 问题解决方案，按文件维度)

```markdown
## ClassesConfig.java

### Import 问题 1: ModelLabel 类导入 (🟢 绿色标记)

- **问题位置**: 行号 3, 19
- **解决方案**: import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;
- **修复操作**: 在文件顶部添加导入语句
```

#### 3. **out\task-abandon.md** (废弃 API 问题解决方案，按文件维度)

```markdown
## TeamEnergyServiceImpl.java

### 废弃 API 问题 1: CommonUtils.calcDouble 废弃 (🟢 绿色标记)

- **问题位置**: 行号 94
- **解决方案**: 使用 NumberCalcUtils.calcDouble 替代
- **修复操作**: 替换方法调用
```

#### 4. **out\task-message.md** (消息推送变更问题解决方案，按文件维度)

#### 5. **out\task-permission.md** (权限 ID 调整问题解决方案，按文件维度)

#### 6. **out\task-unit.md** (单位服务变更问题解决方案，按文件维度)

#### 7. **out\task-quantity.md** (物理量查询服务问题解决方案，按文件维度)

#### 8. **out\task-other.md** (其他类型问题解决方案，按文件维度)

#### 9. **out\task-step.md** (第一轮执行任务，按文件维度 + checkbox)

```markdown
- [x] 2. 修复 ClassesConfig.java 的 Import 和继承问题
  - [x] 2.1 添加 ModelLabel 导入 (行号: 3, 19)
  - [x] 2.4 修复继承关系变更 (行号: 20)
```

#### 10. **out\task-refactor.md** (第二轮方法重构问题解决方案，按文件维度)

#### 11. **out\task-multitenant.md** (第二轮多租户问题解决方案，按文件维度)

#### 12. **out\task-step-round2.md** (第二轮执行任务，按文件维度 + checkbox)

````

**格式统一的优势：**

- ✅ **一致性**: 所有文件都按相同的文件维度组织
- ✅ **可追溯性**: 从原始问题到最终任务，文件结构保持一致
- ✅ **处理效率**: 便于按文件逐个处理和验证
- ✅ **状态管理**: 支持文件级别的进度跟踪

## 工具使用说明

### 核心分析工具

- `error_parser.py`: 解析 JavaAnnotator.xml 错误文件
- `error_grouper.py`: 错误分组和去重处理
- `class_name_finder.py`: 查找替代类名
- `fuzzy_matcher.py`: 查找相似类，如果有多个需要大模型介入进行决策
- `class_file_reader.py`: 读取类文件详细信息（支持源码和 jar 包）
- `git-commit-helper.ps1`: 自动化代码提交

### 参考资料

- `能管代码迁移知识库.md`: 已知问题解决方案
- `JavaAnnotator.xml`: 编译错误源文件
- 项目源码: 需要修复的目标代码

## 📚 经验教训和改进措施

### 🔍 问题根源分析

基于任务 1.2 执行过程中发现的遗漏问题，总结以下经验教训：

#### 1. **文件处理策略问题**

- **问题**: 一次性读取 `out\questionlist.md` 导致内容截断
- **原因**: 文件包含 247 个问题，内容过于庞大
- **教训**: 大文件必须分段处理，不能依赖一次性加载

#### 2. **验证机制不足**

- **问题**: 缺少实时验证，只在最后进行检查
- **原因**: 没有建立处理过程中的检查点
- **教训**: 必须建立多层次、实时的验证机制

#### 3. **问题追踪缺失**

- **问题**: 没有建立问题的唯一标识和追踪机制
- **原因**: 依赖人工记忆和估算，缺少系统化追踪
- **教训**: 必须为每个问题建立唯一标识符

#### 4. **处理范围不完整**

- **问题**: 遗漏了 TeamEnergyController、TeamEnergyService 等文件的问题
- **原因**: 处理策略不够系统化，存在盲区
- **教训**: 必须建立完整的文件清单和覆盖验证

### 🛠️ 改进措施

#### 1. **强制分段处理**

```markdown
- 每次最多处理 10-20 个问题
- 按文件名或问题类型分段
- 每段处理完立即验证
- 建立处理进度跟踪表
````

#### 2. **多重验证机制**

```markdown
- 处理前验证：统计源问题总数
- 处理中验证：实时统计已处理数量
- 处理后验证：最终数量核对
- 质量验证：检查解决方案完整性
```

#### 3. **问题追踪系统**

```markdown
- 唯一标识符：文件名+行号+问题描述
- 处理状态跟踪：未处理/处理中/已完成
- 映射关系验证：源问题 ↔ 解决方案
- 遗漏检查机制：自动发现未处理问题
```

#### 4. **文件覆盖保证**

```markdown
- 建立涉及文件的完整清单
- 逐文件验证问题处理情况
- 交叉验证：按文件、按类型、按行号
- 盲区检查：确保没有遗漏的文件或问题类型
```

### 📈 质量保证措施

#### 1. **执行前检查**

- [ ] 确认分段处理策略
- [ ] 建立问题统计基线
- [ ] 准备验证检查清单
- [ ] 设置处理进度跟踪

#### 2. **执行中监控**

- [ ] 实时统计处理数量
- [ ] 每段处理后立即验证
- [ ] 记录处理进度和状态
- [ ] 及时发现和处理异常

#### 3. **执行后验证**

- [ ] 最终数量核对
- [ ] 问题映射关系验证
- [ ] 解决方案质量检查
- [ ] 生成完整性验证报告

### 🎯 成功标准

一个任务被认为成功完成，必须满足：

1. **数量完整性**: 处理问题数量 = 源问题数量
2. **映射完整性**: 每个源问题都有对应的解决方案
3. **文件完整性**: 所有涉及文件都被完整处理
4. **质量完整性**: 所有解决方案都可执行且准确
5. **验证完整性**: 通过多重验证机制确认

### 🔄 持续改进

基于每次任务执行的经验：

1. **记录问题**: 详细记录遇到的问题和解决方案
2. **更新规范**: 及时更新任务执行规范和要求
3. **优化流程**: 持续优化处理流程和验证机制
4. **分享经验**: 将经验教训应用到后续类似任务中

**最终目标**: 建立一套可靠、完整、高质量的任务执行体系，确保零遗漏、零错误。
